defmodule MisReportsWeb.PrudentialReportLive.Index do
  use MisReportsWeb, :live_view
  require Logger

  # All 73 prudential schedules (2 main reports + 71 individual schedules)
  @prudential_schedules [
    # Main Reports (2)y
    "bal_sheet",
    "income_stmt",
    # Individual Schedules (71)
    "schedule_01c", "schedule_01d", "schedule_01e", "schedule_01f",
    "schedule_02a1", "schedule_02c", "schedule_02d", "schedule_02e",
    "schedule_02f", "schedule_02g", "schedule_02h", "schedule_02j",
    "schedule_03a", "schedule_03b", "schedule_04b", "schedule_04c",
    "schedule_04d", "schedule_05b", "schedule_05c", "schedule_05d",
    "schedule_06a", "schedule_06b", "schedule_07a", "schedule_08a",
    "schedule_08b", "schedule_09a", "schedule_10c", "schedule_11d",
    "schedule_11e", "schedule_11f", "schedule_11g", "schedule_11h",
    "schedule_11j", "schedule_11k", "schedule_12", "schedule_13",
    "schedule_14", "schedule_15", "schedule_17a", "schedule_17b",
    "schedule_17c", "schedule_17e", "schedule_18a", "schedule_18b",
    "schedule_18c", "schedule_18d", "schedule_19", "schedule_20a",
    "schedule_21a", "schedule_21b", "schedule_21c", "schedule_22a",
    "schedule_22b", "schedule_23a", "schedule_23b", "schedule_24",
    "schedule_25", "schedule_26", "schedule_27", "schedule_27a",
    "schedule_28a", "schedule_28b", "schedule_29a", "schedule_30a",
    "schedule_30b", "schedule_30c", "schedule_30d", "schedule_31a",
    "schedule_31b", "schedule_31c", "schedule_31d", "schedule_31e",
    "schedule_31f", "schedule_32a"
  ]



  defp check_task_completion(task_details) when is_map(task_details) do
    case task_details do
      %{steps: steps} when is_list(steps) ->
        # Check if all steps have end_date not nil
        last_step = List.last(steps)
        Map.get(last_step, :end_date) != nil

      steps when is_list(steps) ->
        # Direct list of steps
        last_step = List.last(steps)
        Map.get(last_step, :end_date) != nil

      _ ->
        false
    end
  end

  defp check_task_completion(_), do: false

  # Add helper function for template use
  defp get_last_step(task_details) when is_map(task_details) do
    case task_details do
      %{steps: steps} when is_list(steps) -> List.last(steps)
      steps when is_list(steps) -> List.last(steps)
      _ -> nil
    end
  end

  defp get_last_step(_), do: nil

  # Add helper to get step status
  defp get_step_status(step) when is_map(step) do
    cond do
      is_nil(step) -> "pending"
      Map.get(step, :end_date) -> "completed"
      true -> "pending"
    end
  end

  defp get_step_status(_), do: "pending"

  # Add helper to safely get task completion status
  defp is_task_completed?(task_details) do
    with true <- is_map(task_details),
         %{steps: steps} when is_list(steps) <- task_details,
         last_step when not is_nil(last_step) <- List.last(steps) do
      Map.get(last_step, :end_date) != nil
    else
      _ -> false
    end
  end

  # Helper to safely get step name
  defp get_step_name(task_details) do
    with true <- is_map(task_details),
         %{steps: steps} when is_list(steps) <- task_details,
         last_step when not is_nil(last_step) <- List.last(steps) do
      Map.get(last_step, :step_name) || "Step #{Map.get(last_step, :step_id, "Unknown")}"
    else
      _ -> "Pending"
    end
  end

  @impl true
  def mount(params, session, socket) do
    current_user = MisReports.Accounts.get_user!(session["current_user"])
    reference = params["reference"]

    # Get lists of all possible items
    all_credit_files = MisReports.Enums.FileTypes.get_credit_uploads()
    all_finance_files = MisReports.Enums.FileTypes.get_finance_uploads()
    all_credit_maintenance = MisReports.Enums.FileTypes.get_credit_maintenance()
    all_finance_maintenance = MisReports.Enums.FileTypes.get_finance_maintenance()

    # Get both mandatory and all tasks (including excluded)
    credit_files = MisReports.Workflow.filter_existing_tasks(all_credit_files, reference, false)
    finance_files = MisReports.Workflow.filter_existing_tasks(all_finance_files, reference, false)

    credit_maintenance =
      MisReports.Workflow.filter_existing_tasks(all_credit_maintenance, reference, false)

    finance_maintenance =
      MisReports.Workflow.filter_existing_tasks(all_finance_maintenance, reference, false)

    # Get all tasks for display
    all_tasks = credit_files ++ finance_files ++ credit_maintenance ++ finance_maintenance

    # Filter mandatory tasks for percentage calculation
    mandatory_tasks =
      Enum.reject(all_tasks, fn task ->
        task.process_code in MisReports.Workflow.excluded_process_ids()
      end)

    # Calculate completion percentage using only mandatory tasks
    total_mandatory = length(mandatory_tasks)

    completed_mandatory =
      Enum.count(mandatory_tasks, fn item ->
        MisReports.Workflow.is_main_task_complete?(item.process_code, reference)
      end)

    completion_percentage =
      if total_mandatory > 0, do: round(completed_mandatory / total_mandatory * 100), else: 0

    socket =
      socket
      |> assign(:reference, reference)
      |> assign(:completion_percentage, completion_percentage)
      |> assign(:total_mandatory, total_mandatory)
      |> assign(:completed_mandatory, completed_mandatory)
      |> assign(:current_user, current_user)
      |> assign(:credit_files, credit_files)
      |> assign(:finance_files, finance_files)
      |> assign(:credit_maintenance, credit_maintenance)
      |> assign(:finance_maintenance, finance_maintenance)
      |> assign(:user_role, MisReports.Accounts.get_user_role!(current_user.role_id))
      |> assign(:process_id, params["process_id"])
      |> assign(:step_id, params["step_id"])
      |> assign(:report_type, "")

    {:ok, socket}
  end

  @impl true
  def handle_event("show_modal", _params, socket) do
    {:noreply, assign(socket, :show_modal, true)}
  end

  @impl true
  def handle_event("hide_modal", _params, socket) do
    {:noreply, assign(socket, :show_modal, false)}
  end



  @impl true
  def handle_event("close_comment_modal", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_comment_modal, false)
     |> assign(:current_schedule, nil)
     |> assign(:current_schedule_name, nil)
     |> assign(:modal_comment, "")}
  end

  @impl true
  def handle_event("delete_comment", %{"schedule" => schedule}, socket) do
    # **ROBUST DUPLICATE PREVENTION - REBUILD COMMENT TEXT FROM SCRATCH**
    # 1. Get current state and remove the specified schedule
    pending_comments = socket.assigns.pending_comments || %{}
    updated_pending_comments = Map.delete(pending_comments, schedule)

    # 2. Rebuild the entire comment text from scratch using ONLY the remaining comments
    rebuilt_comment =
      updated_pending_comments
      |> Enum.map(fn {schedule_key, comment_text} ->
        "#{format_schedule_name(schedule_key)}: #{comment_text}"
      end)
      |> Enum.sort()  # Sort for consistent ordering
      |> Enum.join("\n")

    schedule_name = format_schedule_name(schedule)

    {:noreply,
     socket
     |> assign(:comment, rebuilt_comment)
     |> assign(:pending_comments, updated_pending_comments)
     |> put_flash(:info, "Comment deleted for #{schedule_name}")}
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    # Get additional comments from form
    additional_comments = Map.get(params, "additional_comments", "")

    # Combine schedule comments with additional comments
    schedule_comments = socket.assigns.comment || ""

    final_comment = case {String.trim(schedule_comments), String.trim(additional_comments)} do
      {"", ""} -> ""
      {schedule, ""} -> schedule
      {"", additional} -> additional
      {schedule, additional} -> schedule <> "\n\n" <> additional
    end

    # Store additional comments in assigns for persistence
    socket = assign(socket, :additional_comments, additional_comments)

    # Proceed with workflow action
    case action do
      "96" -> handle_reject(socket, final_comment)
      "97" -> handle_approve(socket, final_comment)
      _ -> {:noreply, socket |> put_flash(:error, "Invalid action")}
    end
  end

  # Handle pending comments data from client - ENHANCED WITH SYNC FIX
  @impl true
  def handle_event("pending_comments_data", %{"comments" => comments_json}, socket) do
    case Jason.decode(comments_json) do
      {:ok, comments_map} ->
        combined_comments = combine_pending_comments(comments_map, socket.assigns.reference)

        # Update both the display comment and the persistent storage
        reference_number = socket.assigns.reference
        comment_log_key = "#{reference_number}_comment_log"

        # Update pending_comments with the synchronized data
        updated_pending_comments = socket.assigns.pending_comments
        |> Map.merge(comments_map)  # Merge individual schedule comments
        |> Map.put(comment_log_key, combined_comments)  # Update comment log

        {:noreply,
         socket
         |> assign(:comment, combined_comments)
         |> assign(:pending_comments, updated_pending_comments)}

      {:error, _} ->
        {:noreply, socket}
    end
  end



  # Add these helper functions right after the mount or other handler functions
  defp is_mandatory?(process_code) when is_integer(process_code) do
    excluded_ids = MisReports.Workflow.excluded_process_ids()

    if Enum.member?(excluded_ids, process_code) do
      false
    else
      true
    end
  end

  defp is_mandatory?(_), do: raise("Invalid process code type")

  defp task_status_class(is_completed, is_mandatory)
       when is_boolean(is_completed) and is_boolean(is_mandatory) do
    case {is_completed, is_mandatory} do
      {true, true} -> "text-blue-600 font-medium"
      {false, true} -> "text-gray-900 font-medium"
      {true, false} -> "text-gray-600"
      {false, false} -> "text-gray-400"
    end
  end

  defp task_status_class(_, _), do: raise("Invalid parameters for task_status_class")

  # Add the missing combine_pending_comments function
  defp combine_pending_comments(comments_map, reference) do
    # Filter comments that belong to the current reference
    reference_prefix = "#{reference}_"

    comments_map
    |> Enum.filter(fn {key, _} -> String.starts_with?(key, reference_prefix) end)
    |> Enum.map(fn {key, comment} ->
      # Extract schedule ID from the key
      schedule = String.replace_prefix(key, reference_prefix, "")
      # Format the schedule name
      schedule_name = format_schedule_name(schedule)
      # Format the comment
      "[#{schedule_name}] - #{comment}"
    end)
    |> Enum.join("\n")
  end

  # Add the missing workflow handler functions
  defp handle_reject(socket, comment) do
    current_user_id = to_string(socket.assigns.current_user.id)

    case MisReports.Workflow.call_workflow(
           socket.assigns.reference,
           socket.assigns.process_id,
           current_user_id,
           96,  # Rejection action code
           "",
           "",
           comment
         ) do
      {:ok, _reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Prudential report rejected successfully")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Rejection failed: #{reason}")
         |> assign(:loader, false)}
    end
  end

  defp handle_approve(socket, comment) do
    current_user_id = to_string(socket.assigns.current_user.id)

    case MisReports.Workflow.call_workflow(
           socket.assigns.reference,
           socket.assigns.process_id,
           current_user_id,
           97,  # Approval action code
           "",
           "",
           comment
         ) do
      {:ok, _reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Prudential report approved successfully")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Approval failed: #{reason}")
         |> assign(:loader, false)}
    end
  end

  # Add the missing format_schedule_name function
  defp format_schedule_name(schedule) do
    case schedule do
      "bal_sheet" -> "Balance Sheet"
      "income_stmt" -> "Income Statement"
      "schedule_01c" -> "Schedule 01C"
      "schedule_01d" -> "Schedule 01D"
      "schedule_01e" -> "Schedule 01E"
      "schedule_01f" -> "Schedule 01F"
      "schedule_02a1" -> "Schedule 02A1"
      "schedule_02c" -> "Schedule 02C"
      "schedule_02d" -> "Schedule 02D"
      "schedule_02e" -> "Schedule 02E"
      "schedule_02f" -> "Schedule 02F"
      "schedule_02g" -> "Schedule 02G"
      "schedule_02h" -> "Schedule 02H"
      "schedule_02j" -> "Schedule 02J"
      "schedule_03a" -> "Schedule 03A"
      "schedule_03b" -> "Schedule 03B"
      "schedule_04b" -> "Schedule 04B"
      "schedule_04c" -> "Schedule 04C"
      "schedule_04d" -> "Schedule 04D"
      "schedule_05b" -> "Schedule 05B"
      "schedule_05c" -> "Schedule 05C"
      "schedule_05d" -> "Schedule 05D"
      "schedule_06a" -> "Schedule 06A"
      "schedule_06b" -> "Schedule 06B"
      "schedule_07a" -> "Schedule 07A"
      "schedule_08a" -> "Schedule 08A"
      "schedule_08b" -> "Schedule 08B"
      "schedule_09a" -> "Schedule 09A"
      "schedule_10c" -> "Schedule 10C"
      "schedule_11d" -> "Schedule 11D"
      "schedule_11e" -> "Schedule 11E"
      "schedule_11f" -> "Schedule 11F"
      "schedule_11g" -> "Schedule 11G"
      "schedule_11h" -> "Schedule 11H"
      "schedule_11j" -> "Schedule 11J"
      "schedule_11k" -> "Schedule 11K"
      "schedule_12" -> "Schedule 12"
      "schedule_13" -> "Schedule 13"
      "schedule_14" -> "Schedule 14"
      "schedule_15" -> "Schedule 15"
      "schedule_17a" -> "Schedule 17A"
      "schedule_17b" -> "Schedule 17B"
      "schedule_17c" -> "Schedule 17C"
      "schedule_17e" -> "Schedule 17E"
      "schedule_18a" -> "Schedule 18A"
      "schedule_18b" -> "Schedule 18B"
      "schedule_18c" -> "Schedule 18C"
      "schedule_18d" -> "Schedule 18D"
      "schedule_19" -> "Schedule 19"
      "schedule_20a" -> "Schedule 20A"
      "schedule_21a" -> "Schedule 21A"
      "schedule_21b" -> "Schedule 21B"
      "schedule_21c" -> "Schedule 21C"
      "schedule_22a" -> "Schedule 22A"
      "schedule_22b" -> "Schedule 22B"
      "schedule_23a" -> "Schedule 23A"
      "schedule_23b" -> "Schedule 23B"
      "schedule_24" -> "Schedule 24"
      "schedule_25" -> "Schedule 25"
      "schedule_26" -> "Schedule 26"
      "schedule_27" -> "Schedule 27"
      "schedule_27a" -> "Schedule 27A"
      "schedule_28a" -> "Schedule 28A"
      "schedule_28b" -> "Schedule 28B"
      "schedule_29a" -> "Schedule 29A"
      "schedule_30a" -> "Schedule 30A"
      "schedule_30b" -> "Schedule 30B"
      "schedule_30c" -> "Schedule 30C"
      "schedule_30d" -> "Schedule 30D"
      "schedule_31a" -> "Schedule 31A"
      "schedule_31b" -> "Schedule 31B"
      "schedule_31c" -> "Schedule 31C"
      "schedule_31d" -> "Schedule 31D"
      "schedule_31e" -> "Schedule 31E"
      "schedule_31f" -> "Schedule 31F"
      "schedule_32a" -> "Schedule 32A"
      _ -> String.upcase(schedule)
    end
  end

end
